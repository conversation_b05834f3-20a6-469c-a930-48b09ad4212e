"""
会员消费SQL查询扩展模块
作为 MemberConsumeSql.py 的补充，提供额外的消费分析功能
"""
import asyncio
import logging
import math
from typing import Optional, List, Dict, Any
from core.database import Database

# 配置日志
logger = logging.getLogger(__name__)

class MemberConsumeSqlAddQueries:
    """会员消费数据扩展SQL查询类"""

    @staticmethod
    async def get_dwoutput_first_consume_amount_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> float:
        """获取首次消费金额（优化版本 - 无JOIN，分批处理）

        数据库：dwoutput.dprpt_welife_trade_consume_detail
        字段：tcfee（手续费）, tcstoredpay（储值支付）, tclprinciple（本金）, tcType（交易类型）
        优化策略：
        - 第一步：查询每个会员的全局首次消费记录ID (MIN(tcid))
        - 第二步：分批查询这些首次消费记录的详情并按时间范围过滤
        - 在应用层进行数据整合计算，而不是在SQL层
        - 使用分批处理避免大IN子句对数据库造成压力
        - 每批处理完成后清理内存，避免内存压力

        注意：此函数已从返回SQL字符串改为直接返回计算结果，这是一个破坏性变更
        如需旧版本SQL字符串，请参考代码注释中的备份实现

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            首次消费金额（浮点数）
        """
        batch_size = 1000  # 每批处理1000个tcid，避免IN子句过长
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        # 初始化数据库连接
        db_manager = Database()
        await db_manager.connect()

        try:
            # 第一步：找出每个会员的全局首次消费记录ID（不限时间）
            first_tcid_sql = f"""
            SELECT uno, MIN(tcid) AS first_tcid
            FROM dwoutput.dprpt_welife_trade_consume_detail
            WHERE bid = {bid}
              {sid_condition}
            GROUP BY uno
            """

            first_tcid_records = await db_manager.execute_dwoutput_query(first_tcid_sql)

            if not first_tcid_records:
                return 0.0

            # 准备分批处理
            first_tcids = [str(record['first_tcid']) for record in first_tcid_records]
            total_tcids = len(first_tcids)
            batch_count = math.ceil(total_tcids / batch_size)

            # 第二步：分批查询首次消费记录详情
            total_first_consume_amount = 0.0

            for batch_idx in range(batch_count):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, total_tcids)
                batch_tcids = first_tcids[start_idx:end_idx]
                tcid_list = ','.join(batch_tcids)

                detail_sql = f"""
                SELECT
                    uno,
                    tcid,
                    tcfee,
                    tcstoredpay,
                    tclprinciple,
                    tcType,
                    ftime
                FROM dwoutput.dprpt_welife_trade_consume_detail
                WHERE bid = {bid}
                  AND tcid IN ({tcid_list})
                  AND ftime BETWEEN {start_date} AND {end_date}
                  {sid_condition}
                """

                batch_records = await db_manager.execute_dwoutput_query(detail_sql)

                # 应用层计算当前批次的首次消费金额
                batch_amount = 0.0
                for record in batch_records:
                    tcType = record['tcType']

                    if tcType == 2:  # 正常消费
                        amount = (record['tcfee'] or 0) + (record['tcstoredpay'] or 0)
                        batch_amount += amount
                    elif tcType == 3:  # 取消消费
                        amount = (record['tcfee'] or 0) + (record['tclprinciple'] or 0)
                        batch_amount -= amount

                total_first_consume_amount += batch_amount

                # 清理当前批次数据，释放内存
                del batch_records
                del batch_tcids

                # 添加小延迟，避免对数据库造成过大压力
                if batch_idx < batch_count - 1:  # 最后一批不需要延迟
                    await asyncio.sleep(0.1)  # 100ms延迟

            # 清理所有临时数据
            del first_tcids
            del first_tcid_records

            return round(total_first_consume_amount, 2)

        except Exception as e:
            raise e



    @staticmethod
    def get_dwoutput_consume_frequency_stats_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员消费频次统计查询SQL

        数据库：dprpt_welife_trade_consume_detail
        字段：uno（卡号）
        计算方式：统计不同消费次数的会员数量
        说明：
        - 消费1次的会员数量
        - 消费2次的会员数量
        - 消费3次的会员数量
        - 消费3次以上会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            会员消费频次统计查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN {start_date} AND {end_date}
            AND bid = {bid}
            {sid_condition}
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        """

    @staticmethod
    def get_dwoutput_first_consume_amount_optimized_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取首次消费金额查询SQL（优化版本 - 无JOIN）

        数据库：dwoutput.dprpt_welife_trade_consume_detail
        字段：uno（卡号）, tcid（交易ID）, tcfee（手续费）, tcstoredpay（储值支付）,
              tclprinciple（本金）, tcType（交易类型）, ftime（交易时间）
        优化策略：
        - 去掉JOIN操作，改为简单的WHERE条件查询
        - 在应用层进行首次消费记录筛选和金额计算
        - 按时间范围查询所有消费记录，然后在Python中处理

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            优化后的消费记录查询SQL（返回所有记录，在应用层筛选首次消费）
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT
            uno,
            tcid,
            tcfee,
            tcstoredpay,
            tclprinciple,
            tcType,
            ftime
        FROM dwoutput.dprpt_welife_trade_consume_detail
        WHERE bid = {bid}
          AND ftime BETWEEN {start_date} AND {end_date}
          {sid_condition}
        ORDER BY uno, tcid
        """

class MemberConsumeCalculatorAdd:
    """会员消费数据扩展计算器"""

    @staticmethod
    def calculate_repeat_consume_amount(total_actual_amount: float, first_consume_amount: float) -> float:
        """计算再次消费金额

        计算公式：再次消费金额 = 会员总实收金额 - 首次消费金额

        Args:
            total_actual_amount: 会员总实收金额
            first_consume_amount: 首次消费金额

        Returns:
            再次消费金额，保留两位小数
        """
        try:
            # 确保数据类型转换
            total_actual_amount = float(total_actual_amount) if total_actual_amount is not None else 0.0
            first_consume_amount = float(first_consume_amount) if first_consume_amount is not None else 0.0

            repeat_amount = total_actual_amount - first_consume_amount
            result = round(repeat_amount, 2)

            logger.debug(f"再次消费金额计算: {total_actual_amount} - {first_consume_amount} = {result}")
            return result

        except Exception as e:
            logger.error(f"计算再次消费金额失败: {str(e)}")
            return 0.0

    @staticmethod
    def calculate_repurchase_rate(consume_twice_members: int, consume_thrice_members: int,
                                consume_more_than_thrice_members: int, total_consume_uv: int) -> float:
        """计算复购率

        计算公式：复购率 = 消费2次及以上的人数 / 会员消费人数

        Args:
            consume_twice_members: 消费2次的会员数量
            consume_thrice_members: 消费3次的会员数量
            consume_more_than_thrice_members: 消费3次以上会员数量
            total_consume_uv: 会员消费人数

        Returns:
            复购率（百分比），保留两位小数
        """
        try:
            # 确保数据类型转换
            consume_twice_members = int(consume_twice_members) if consume_twice_members is not None else 0
            consume_thrice_members = int(consume_thrice_members) if consume_thrice_members is not None else 0
            consume_more_than_thrice_members = int(consume_more_than_thrice_members) if consume_more_than_thrice_members is not None else 0
            total_consume_uv = int(total_consume_uv) if total_consume_uv is not None else 0

            if total_consume_uv == 0:
                logger.warning("会员消费人数为0，复购率无法计算")
                return 0.0

            # 计算消费2次及以上的人数
            repeat_consumers = consume_twice_members + consume_thrice_members + consume_more_than_thrice_members

            # 计算复购率（百分比）
            repurchase_rate = (repeat_consumers / total_consume_uv) * 100
            result = round(repurchase_rate, 2)

            logger.debug(f"复购率计算: ({consume_twice_members} + {consume_thrice_members} + {consume_more_than_thrice_members}) / {total_consume_uv} * 100 = {result}%")
            return result

        except Exception as e:
            logger.error(f"计算复购率失败: {str(e)}")
            return 0.0

    @staticmethod
    def calculate_prepay_consumption_ratio(prepay_used_real: float, consume_cash_real: float) -> float:
        """计算储值消费实收金额占比

        计算公式：储值消费实收金额占比 = 会员使用储值的实收金额 / (会员实收金额 + 会员使用储值的实收金额)

        Args:
            prepay_used_real: 会员使用储值的实收金额
            consume_cash_real: 会员实收金额

        Returns:
            储值消费实收金额占比（百分比），保留两位小数
        """
        try:
            # 确保数据类型转换
            prepay_used_real = float(prepay_used_real) if prepay_used_real is not None else 0.0
            consume_cash_real = float(consume_cash_real) if consume_cash_real is not None else 0.0

            # 计算总实收金额
            total_real_amount = consume_cash_real + prepay_used_real

            if total_real_amount == 0:
                logger.warning("总实收金额为0，储值消费实收金额占比无法计算")
                return 0.0

            # 计算储值消费实收金额占比（百分比）
            prepay_ratio = (prepay_used_real / total_real_amount) * 100
            result = round(prepay_ratio, 2)

            logger.debug(f"储值消费实收金额占比计算: {prepay_used_real} / ({consume_cash_real} + {prepay_used_real}) * 100 = {result}%")
            return result

        except Exception as e:
            logger.error(f"计算储值消费实收金额占比失败: {str(e)}")
            return 0.0

    @staticmethod
    def merge_consume_frequency_stats(base_data: dict, frequency_stats_data: dict) -> dict:
        """合并消费频次统计数据

        Args:
            base_data: 基础数据
            frequency_stats_data: 消费频次统计数据

        Returns:
            合并后的数据字典
        """
        try:
            logger.debug("开始合并消费频次统计数据")

            # 合并基础数据
            result = base_data.copy()

            # 添加消费频次统计数据
            result['consume_once_members'] = frequency_stats_data.get('consume_once_members', 0) or 0
            result['consume_twice_members'] = frequency_stats_data.get('consume_twice_members', 0) or 0
            result['consume_thrice_members'] = frequency_stats_data.get('consume_thrice_members', 0) or 0
            result['consume_more_than_thrice_members'] = frequency_stats_data.get('consume_more_than_thrice_members', 0) or 0

            logger.debug(f"消费频次统计数据合并完成，新增字段: consume_once_members, consume_twice_members, consume_thrice_members, consume_more_than_thrice_members")
            return result

        except Exception as e:
            logger.error(f"合并消费频次统计数据失败: {str(e)}")
            return base_data

    @staticmethod
    def merge_additional_consume_data(base_data: dict) -> dict:
        """合并额外的消费数据（计算复购率和储值消费实收金额占比）

        Args:
            base_data: 基础消费数据

        Returns:
            合并后的数据字典
        """
        try:
            logger.debug("开始合并额外消费数据")

            # 合并基础数据
            result = base_data.copy()

            # 计算复购率
            consume_twice_members = result.get('consume_twice_members', 0) or 0
            consume_thrice_members = result.get('consume_thrice_members', 0) or 0
            consume_more_than_thrice_members = result.get('consume_more_than_thrice_members', 0) or 0
            total_consume_uv = result.get('total_consume_uv', 0) or 0

            repurchase_rate = MemberConsumeCalculatorAdd.calculate_repurchase_rate(
                consume_twice_members, consume_thrice_members, consume_more_than_thrice_members, total_consume_uv
            )
            result['repurchase_rate'] = repurchase_rate

            # 计算储值消费实收金额占比
            prepay_used_real = result.get('total_prepay_used_real', 0) or 0
            consume_cash_real = result.get('total_consume_cash_real', 0) or 0
            prepay_consumption_ratio = MemberConsumeCalculatorAdd.calculate_prepay_consumption_ratio(
                prepay_used_real, consume_cash_real
            )
            result['prepay_consumption_ratio'] = prepay_consumption_ratio

            logger.debug(f"额外消费数据合并完成，新增字段: repurchase_rate, prepay_consumption_ratio")
            return result

        except Exception as e:
            logger.error(f"合并额外消费数据失败: {str(e)}")
            return base_data


# 备份：旧版本JOIN查询SQL实现（已废弃）
"""
get_dwoutput_first_consume_amount_sql 旧版本实现（仅供参考）：

def get_dwoutput_first_consume_amount_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
    sid_condition = f"AND a.sid = '{sid}'" if sid else ""
    sid_condition_subquery = f"AND sid = '{sid}'" if sid else ""

    return f'''
    SELECT
      COALESCE(SUM(CASE WHEN a.tcType = 2 THEN a.tcfee + a.tcstoredpay ELSE 0 END), 0) -
      COALESCE(SUM(CASE WHEN a.tcType = 3 THEN a.tcfee + a.tclprinciple ELSE 0 END), 0) AS first_consume_amount
    FROM dwoutput.dprpt_welife_trade_consume_detail a
    JOIN (
        SELECT uno, MIN(tcid) AS first_tcid
        FROM dwoutput.dprpt_welife_trade_consume_detail
        WHERE bid = {bid}
          {sid_condition_subquery}
        GROUP BY uno
    ) b ON a.uno = b.uno AND a.tcid = b.first_tcid
    WHERE a.bid = {bid}
      AND a.ftime BETWEEN {start_date} AND {end_date}
      {sid_condition}
    '''
"""