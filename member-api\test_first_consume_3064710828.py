#!/usr/bin/env python3
"""
首次消费金额测试脚本 - 专门测试 bid=3064710828
测试时间范围：20250601-20250630

使用优化的分批处理方法，无JOIN查询，性能更好
函数名保持为 get_dwoutput_first_consume_amount_sql
"""

import asyncio
import logging
import math
from typing import Optional

# 导入数据库连接
from core.database import Database

# 配置日志 - 记录实际发生时间
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

async def get_dwoutput_first_consume_amount_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> float:
    """获取首次消费金额（优化版本 - 无JOIN，分批处理）

    数据库：dwoutput.dprpt_welife_trade_consume_detail
    字段：tcfee（手续费）, tcstoredpay（储值支付）, tclprinciple（本金）, tcType（交易类型）
    优化策略：
    - 第一步：查询每个会员的全局首次消费记录ID (MIN(tcid))
    - 第二步：分批查询这些首次消费记录的详情并按时间范围过滤
    - 在应用层进行数据整合计算，而不是在SQL层
    - 使用分批处理避免大IN子句对数据库造成压力
    - 每批处理完成后清理内存，避免内存压力

    Args:
        start_date: 开始日期 (YYYYMMDD格式)
        end_date: 结束日期 (YYYYMMDD格式)
        bid: 品牌ID
        sid: 门店ID (可选)

    Returns:
        首次消费金额（浮点数）
    """
    batch_size = 1000  # 每批处理1000个tcid，避免IN子句过长
    sid_condition = f"AND sid = '{sid}'" if sid else ""

    logger.info(f"🚀 开始优化的首次消费查询: bid={bid}, 时间范围={start_date}-{end_date}")

    # 初始化数据库连接
    db_manager = Database()
    await db_manager.connect()

    try:
        # 第一步：找出每个会员的全局首次消费记录ID（不限时间）
        first_tcid_sql = f"""
        SELECT uno, MIN(tcid) AS first_tcid
        FROM dwoutput.dprpt_welife_trade_consume_detail
        WHERE bid = {bid}
          {sid_condition}
        GROUP BY uno
        """

        logger.info("📊 第一步：查询每个会员的首次消费记录ID")
        first_tcid_records = await db_manager.execute_dwoutput_query(first_tcid_sql)
        logger.info(f"✅ 第一步完成，获得 {len(first_tcid_records)} 个会员的首次消费记录")

        if not first_tcid_records:
            logger.warning("⚠️  没有找到任何会员的首次消费记录")
            return 0.0

        # 准备分批处理
        first_tcids = [str(record['first_tcid']) for record in first_tcid_records]
        total_tcids = len(first_tcids)
        batch_count = math.ceil(total_tcids / batch_size)

        logger.info(f"🔧 准备分批处理: 总计{total_tcids}条记录，分{batch_count}批，每批{batch_size}条")

        # 第二步：分批查询首次消费记录详情
        logger.info("📊 第二步：分批查询首次消费记录详情")
        total_first_consume_amount = 0.0

        for batch_idx in range(batch_count):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, total_tcids)
            batch_tcids = first_tcids[start_idx:end_idx]
            tcid_list = ','.join(batch_tcids)

            logger.info(f"📊 批次{batch_idx + 1}/{batch_count}: 处理{len(batch_tcids)}条记录")

            detail_sql = f"""
            SELECT
                uno,
                tcid,
                tcfee,
                tcstoredpay,
                tclprinciple,
                tcType,
                ftime
            FROM dwoutput.dprpt_welife_trade_consume_detail
            WHERE bid = {bid}
              AND tcid IN ({tcid_list})
              AND ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            """

            batch_records = await db_manager.execute_dwoutput_query(detail_sql)

            # 应用层计算当前批次的首次消费金额
            batch_amount = 0.0
            for record in batch_records:
                tcType = record['tcType']

                if tcType == 2:  # 正常消费
                    amount = (record['tcfee'] or 0) + (record['tcstoredpay'] or 0)
                    batch_amount += amount
                elif tcType == 3:  # 取消消费
                    amount = (record['tcfee'] or 0) + (record['tclprinciple'] or 0)
                    batch_amount -= amount

            total_first_consume_amount += batch_amount
            logger.info(f"✅ 批次{batch_idx + 1}完成: {len(batch_records)}条记录，金额{batch_amount:.2f}")

            # 清理当前批次数据，释放内存
            del batch_records
            del batch_tcids

            # 添加小延迟，避免对数据库造成过大压力
            if batch_idx < batch_count - 1:  # 最后一批不需要延迟
                await asyncio.sleep(0.1)  # 100ms延迟

        # 清理所有临时数据
        del first_tcids
        del first_tcid_records

        logger.info(f"✅ 首次消费查询完成，总金额: {total_first_consume_amount:.2f}")
        return round(total_first_consume_amount, 2)

    except Exception as e:
        logger.error(f"❌ 首次消费查询失败: {str(e)}")
        raise e

async def test_first_consume_calculation():
    """测试首次消费金额计算"""
    
    # 指定的测试参数
    bid = "3064710828"
    start_date = "20250601"
    end_date = "20250630"
    
    logger.info("🛡️ 开始首次消费金额测试")
    logger.info(f"📋 测试条件: bid={bid}, 时间范围={start_date}-{end_date}")
    logger.info("⏰ 开始记录实际执行时间")
    
    try:
        # 执行首次消费金额计算
        first_consume_amount = await get_dwoutput_first_consume_amount_sql(
            start_date=start_date,
            end_date=end_date,
            bid=bid
        )
        
        # 输出最终结果
        logger.info("="*60)
        logger.info("📊 测试结果汇总")
        logger.info("="*60)
        logger.info(f"📊 品牌ID: {bid}")
        logger.info(f"📊 时间范围: {start_date} - {end_date}")
        logger.info(f"💰 首次消费金额: {first_consume_amount:.2f}")
        logger.info("="*60)
        
        # 验证结果
        if first_consume_amount > 0:
            logger.info("✅ 测试成功：首次消费金额计算完成，结果大于0")
        elif first_consume_amount == 0:
            logger.info("⚠️  测试完成：首次消费金额为0，可能该时间范围内没有首次消费记录")
        else:
            logger.warning("⚠️  测试完成：首次消费金额为负数，可能存在较多取消消费记录")
            
        return first_consume_amount
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return None
        
    finally:
        logger.info("🏁 首次消费金额测试完成")

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(test_first_consume_calculation())
    
    if result is not None:
        print(f"\n🎯 最终结果: 首次消费金额 = {result:.2f}")
    else:
        print("\n❌ 测试失败，请查看日志了解详情")
