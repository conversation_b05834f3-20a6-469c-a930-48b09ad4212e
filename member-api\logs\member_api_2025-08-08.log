2025-08-08 09:54:52 - main - INFO - lifespan:23 - 应用启动中...
2025-08-08 09:54:52 - core.database - INFO - connect:28 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-08 09:54:52 - core.database - INFO - connect:42 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-08 09:54:52 - core.database - INFO - connect:45 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-08 09:54:52 - core.database - INFO - connect:59 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-08 09:54:52 - core.database - INFO - connect:62 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-08 09:54:53 - core.database - INFO - connect:76 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-08 09:54:53 - core.database - INFO - connect:79 - 正在创建basic_info数据库连接... [企业微信数据库，用于存储企业微信相关数据]
2025-08-08 09:54:53 - core.database - INFO - connect:93 - basic_info数据库连接池创建成功: rm-2zety0946b3t6t764ko.mysql.rds.aliyuncs.com:3306/basic_info
2025-08-08 09:54:53 - core.database - INFO - connect:96 - 正在创建backend数据库连接... [企业微信和微生活对应数据库，用于存储商户映射关系]
2025-08-08 09:54:53 - core.database - INFO - connect:110 - backend数据库连接池创建成功: rm-2zety0946b3t6t764ko.mysql.rds.aliyuncs.com:3306/backend
2025-08-08 09:54:53 - core.database - INFO - connect:113 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-08 09:54:53 - core.database - INFO - connect:124 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-08 09:54:53 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-08 09:54:53 - aiomysql - INFO - execute:243 - None
2025-08-08 09:54:53 - core.database - INFO - _test_database_connections:141 - dwoutput数据库连接测试成功: (1, 360550282, 'dwoutput')
2025-08-08 09:54:53 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-08 09:54:53 - aiomysql - INFO - execute:243 - None
2025-08-08 09:54:53 - core.database - INFO - _test_database_connections:148 - wedatas数据库连接测试成功: (1, 628985459, 'wedatas')
2025-08-08 09:54:53 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-08 09:54:53 - aiomysql - INFO - execute:243 - None
2025-08-08 09:54:53 - core.database - INFO - _test_database_connections:157 - welife_hydb数据库连接测试成功: (1,)
2025-08-08 09:54:53 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-08 09:54:53 - aiomysql - INFO - execute:243 - None
2025-08-08 09:54:53 - core.database - INFO - _test_database_connections:163 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-08 09:54:53 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-08 09:54:53 - aiomysql - INFO - execute:243 - None
2025-08-08 09:54:53 - core.database - INFO - _test_database_connections:176 - basic_info数据库连接测试成功: (1, 11089628, 'basic_info')
2025-08-08 09:54:53 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-08 09:54:53 - aiomysql - INFO - execute:243 - None
2025-08-08 09:54:53 - core.database - INFO - _test_database_connections:183 - backend数据库连接测试成功: (1, 11089634, 'backend')
2025-08-08 09:54:53 - core.database - INFO - _test_database_connections:188 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-08 09:54:53 - main - INFO - lifespan:27 - 数据库连接已建立
